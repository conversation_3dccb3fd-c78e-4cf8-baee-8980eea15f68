using System;
using System.Collections.Generic;

namespace Core.CrossCuttingConcerns.Caching.Configuration
{
    public class CacheConfiguration
    {
        public int DefaultDurationMinutes { get; set; } = 60;
        public int MaxCacheSize { get; set; } = 50000; // Global max cache size (deprecated - use tenant limits)
        public bool EnableStatistics { get; set; } = true;
        public bool EnableDebugLogging { get; set; } = false;

        // Tenant bazlı memory quota ayarları
        public TenantQuotaSettings TenantQuota { get; set; } = new();

        // Tenant bazlı cache ayarları
        public Dictionary<string, EntityCacheSettings> EntitySettings { get; set; } = new();
        
        public CacheConfiguration()
        {
            InitializeDefaultEntitySettings();
        }
        
        private void InitializeDefaultEntitySettings()
        {
            // Member cache ayarları
            EntitySettings["Member"] = new EntityCacheSettings
            {
                DefaultDuration = 30,
                Tags = new[] { "Member", "User" },
                InvalidateOnEntityChange = true,
                RelatedEntities = new[] { "Membership", "Payment" }
            };
            
            // Product cache ayarları
            EntitySettings["Product"] = new EntityCacheSettings
            {
                DefaultDuration = 120,
                Tags = new[] { "Product", "Inventory" },
                InvalidateOnEntityChange = true,
                RelatedEntities = new[] { "Transaction" }
            };
            
            // Payment cache ayarları
            EntitySettings["Payment"] = new EntityCacheSettings
            {
                DefaultDuration = 60,
                Tags = new[] { "Payment", "Financial" },
                InvalidateOnEntityChange = true,
                RelatedEntities = new[] { "Member", "Transaction" }
            };
            
            // Membership cache ayarları
            EntitySettings["Membership"] = new EntityCacheSettings
            {
                DefaultDuration = 45,
                Tags = new[] { "Membership", "Member" },
                InvalidateOnEntityChange = true,
                RelatedEntities = new[] { "Member", "Payment" }
            };
            
            // City/Town cache ayarları (static data)
            EntitySettings["City"] = new EntityCacheSettings
            {
                DefaultDuration = 1440, // 24 saat
                Tags = new[] { "Location", "Static" },
                InvalidateOnEntityChange = false,
                RelatedEntities = new[] { "Town" }
            };
            
            EntitySettings["Town"] = new EntityCacheSettings
            {
                DefaultDuration = 1440, // 24 saat
                Tags = new[] { "Location", "Static" },
                InvalidateOnEntityChange = false,
                RelatedEntities = new[] { "City" }
            };
        }
    }
    
    public class EntityCacheSettings
    {
        public int DefaultDuration { get; set; }
        public string[] Tags { get; set; } = Array.Empty<string>();
        public bool InvalidateOnEntityChange { get; set; } = true;
        public string[] RelatedEntities { get; set; } = Array.Empty<string>();
    }

    public class TenantQuotaSettings
    {
        // Her tenant için maksimum memory kullanımı (MB)
        public int MaxMemoryPerTenantMB { get; set; } = 10; // 10MB per tenant

        // Her tenant için maksimum entry sayısı
        public int MaxEntriesPerTenant { get; set; } = 5000; // 5000 entries per tenant

        // Memory pressure threshold (%)
        public int MemoryPressureThreshold { get; set; } = 80; // %80'de temizlik başlar

        // Eviction batch size (kaç entry birden silinir)
        public int EvictionBatchSize { get; set; } = 100;

        // Memory check interval (saniye)
        public int MemoryCheckIntervalSeconds { get; set; } = 30;

        // Enable memory quota enforcement
        public bool EnableMemoryQuota { get; set; } = true;

        // Enable automatic cleanup
        public bool EnableAutoCleanup { get; set; } = true;

        // Log memory quota violations
        public bool LogQuotaViolations { get; set; } = true;
    }
}

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text.Json;

namespace Core.CrossCuttingConcerns.Caching.Models
{
    public class TenantMemoryTracker
    {
        private readonly ConcurrentDictionary<int, TenantMemoryInfo> _tenantMemoryInfo;
        private readonly object _lockObject = new object();

        public TenantMemoryTracker()
        {
            _tenantMemoryInfo = new ConcurrentDictionary<int, TenantMemoryInfo>();
        }

        public void RecordCacheAdd(int tenantId, string key, object value, int durationMinutes)
        {
            var memorySize = EstimateObjectSize(value);
            var tenantInfo = GetOrCreateTenantInfo(tenantId);

            lock (tenantInfo.LockObject)
            {
                tenantInfo.TotalMemoryBytes += memorySize;
                tenantInfo.EntryCount++;
                tenantInfo.LastActivity = DateTime.UtcNow;

                // Entry bilgisini sakla
                tenantInfo.CacheEntries[key] = new CacheEntryInfo
                {
                    Key = key,
                    MemorySize = memorySize,
                    CreatedAt = DateTime.UtcNow,
                    ExpiresAt = DateTime.UtcNow.AddMinutes(durationMinutes),
                    LastAccessed = DateTime.UtcNow
                };
            }
        }

        public void RecordCacheRemove(int tenantId, string key)
        {
            var tenantInfo = GetTenantInfo(tenantId);
            if (tenantInfo == null) return;

            lock (tenantInfo.LockObject)
            {
                if (tenantInfo.CacheEntries.TryRemove(key, out var entryInfo))
                {
                    tenantInfo.TotalMemoryBytes -= entryInfo.MemorySize;
                    tenantInfo.EntryCount--;
                    tenantInfo.LastActivity = DateTime.UtcNow;
                }
            }
        }

        public void RecordCacheAccess(int tenantId, string key)
        {
            var tenantInfo = GetTenantInfo(tenantId);
            if (tenantInfo == null) return;

            lock (tenantInfo.LockObject)
            {
                if (tenantInfo.CacheEntries.TryGetValue(key, out var entryInfo))
                {
                    entryInfo.LastAccessed = DateTime.UtcNow;
                    entryInfo.AccessCount++;
                }
                tenantInfo.LastActivity = DateTime.UtcNow;
            }
        }

        public TenantMemoryInfo GetTenantInfo(int tenantId)
        {
            return _tenantMemoryInfo.TryGetValue(tenantId, out var info) ? info : null;
        }

        public TenantMemoryInfo GetOrCreateTenantInfo(int tenantId)
        {
            return _tenantMemoryInfo.GetOrAdd(tenantId, _ => new TenantMemoryInfo(tenantId));
        }

        public Dictionary<int, TenantMemoryInfo> GetAllTenantInfo()
        {
            return _tenantMemoryInfo.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        }

        public bool IsMemoryLimitExceeded(int tenantId, long maxMemoryBytes)
        {
            var tenantInfo = GetTenantInfo(tenantId);
            return tenantInfo?.TotalMemoryBytes > maxMemoryBytes;
        }

        public bool IsEntryLimitExceeded(int tenantId, int maxEntries)
        {
            var tenantInfo = GetTenantInfo(tenantId);
            return tenantInfo?.EntryCount > maxEntries;
        }

        public List<CacheEntryInfo> GetLeastRecentlyUsedEntries(int tenantId, int count)
        {
            var tenantInfo = GetTenantInfo(tenantId);
            if (tenantInfo == null) return new List<CacheEntryInfo>();

            lock (tenantInfo.LockObject)
            {
                return tenantInfo.CacheEntries.Values
                    .OrderBy(e => e.LastAccessed)
                    .Take(count)
                    .ToList();
            }
        }

        public List<CacheEntryInfo> GetExpiredEntries(int tenantId)
        {
            var tenantInfo = GetTenantInfo(tenantId);
            if (tenantInfo == null) return new List<CacheEntryInfo>();

            var now = DateTime.UtcNow;
            lock (tenantInfo.LockObject)
            {
                return tenantInfo.CacheEntries.Values
                    .Where(e => now > e.ExpiresAt)
                    .ToList();
            }
        }

        public void ClearTenant(int tenantId)
        {
            _tenantMemoryInfo.TryRemove(tenantId, out _);
        }

        public long GetTotalMemoryUsage()
        {
            return _tenantMemoryInfo.Values.Sum(t => t.TotalMemoryBytes);
        }

        public int GetTotalEntryCount()
        {
            return _tenantMemoryInfo.Values.Sum(t => t.EntryCount);
        }

        private long EstimateObjectSize(object obj)
        {
            if (obj == null) return 0;

            try
            {
                // JSON serialization ile yaklaşık boyut hesaplama
                var json = JsonSerializer.Serialize(obj);
                return System.Text.Encoding.UTF8.GetByteCount(json);
            }
            catch
            {
                // Fallback: Type'a göre tahmin
                return obj switch
                {
                    string str => str.Length * 2, // Unicode
                    int => 4,
                    long => 8,
                    double => 8,
                    DateTime => 8,
                    bool => 1,
                    _ => 1024 // Default 1KB
                };
            }
        }
    }

    public class TenantMemoryInfo
    {
        public int TenantId { get; }
        public long TotalMemoryBytes { get; set; }
        public int EntryCount { get; set; }
        public DateTime LastActivity { get; set; }
        public DateTime CreatedAt { get; }
        public object LockObject { get; } = new object();
        
        public ConcurrentDictionary<string, CacheEntryInfo> CacheEntries { get; }

        public TenantMemoryInfo(int tenantId)
        {
            TenantId = tenantId;
            CreatedAt = DateTime.UtcNow;
            LastActivity = DateTime.UtcNow;
            CacheEntries = new ConcurrentDictionary<string, CacheEntryInfo>();
        }

        public double GetMemoryUsageMB()
        {
            return TotalMemoryBytes / (1024.0 * 1024.0);
        }

        public double GetMemoryUsagePercentage(long maxMemoryBytes)
        {
            return maxMemoryBytes > 0 ? (double)TotalMemoryBytes / maxMemoryBytes * 100 : 0;
        }
    }

    public class CacheEntryInfo
    {
        public string Key { get; set; }
        public long MemorySize { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime ExpiresAt { get; set; }
        public DateTime LastAccessed { get; set; }
        public int AccessCount { get; set; }

        public bool IsExpired => DateTime.UtcNow > ExpiresAt;
        
        public TimeSpan Age => DateTime.UtcNow - CreatedAt;
        
        public TimeSpan TimeSinceLastAccess => DateTime.UtcNow - LastAccessed;
    }
}

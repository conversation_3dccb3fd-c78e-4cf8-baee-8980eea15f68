using Business.Abstract;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace WebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class CacheAdminController : ControllerBase
    {
        private readonly ICacheService _cacheService;

        public CacheAdminController(ICacheService cacheService)
        {
            _cacheService = cacheService;
        }

        [HttpGet("statistics")]
        public IActionResult GetStatistics()
        {
            var result = _cacheService.GetStatistics();
            if (result.Success)
                return Ok(result);
            return BadRequest(result);
        }

        [HttpGet("health")]
        public IActionResult GetHealth()
        {
            var result = _cacheService.GetHealthInfo();
            if (result.Success)
                return Ok(result);
            return BadRequest(result);
        }

        [HttpGet("keys")]
        public IActionResult GetAllKeys()
        {
            var result = _cacheService.GetAllKeys();
            if (result.Success)
                return Ok(result);
            return BadRequest(result);
        }

        [HttpGet("keys/pattern/{pattern}")]
        public IActionResult GetKeysByPattern(string pattern)
        {
            var result = _cacheService.GetKeysByPattern(pattern);
            if (result.Success)
                return Ok(result);
            return BadRequest(result);
        }

        [HttpGet("size")]
        public IActionResult GetCacheSize()
        {
            var result = _cacheService.GetCacheSize();
            if (result.Success)
                return Ok(result);
            return BadRequest(result);
        }

        [HttpDelete("clear")]
        public IActionResult ClearAll()
        {
            var result = _cacheService.ClearAll();
            if (result.Success)
                return Ok(result);
            return BadRequest(result);
        }

        [HttpDelete("clear/tenant/{tenantId}")]
        public IActionResult ClearTenant(int tenantId)
        {
            var result = _cacheService.ClearTenant(tenantId);
            if (result.Success)
                return Ok(result);
            return BadRequest(result);
        }

        [HttpDelete("clear/entity/{tenantId}/{entityName}")]
        public IActionResult ClearEntity(int tenantId, string entityName)
        {
            var result = _cacheService.ClearEntity(tenantId, entityName);
            if (result.Success)
                return Ok(result);
            return BadRequest(result);
        }

        [HttpGet("test")]
        public IActionResult TestCache()
        {
            var result = _cacheService.TestCache();
            if (result.Success)
                return Ok(result);
            return BadRequest(result);
        }

        [HttpGet("statistics/detailed")]
        public IActionResult GetDetailedStatistics()
        {
            var result = _cacheService.GetDetailedStatistics();
            if (result.Success)
                return Ok(result);
            return BadRequest(result);
        }

        [HttpGet("statistics/tenant/{tenantId}")]
        public IActionResult GetTenantStatistics(int tenantId)
        {
            var result = _cacheService.GetTenantStatistics(tenantId);
            if (result.Success)
                return Ok(result);
            return BadRequest(result);
        }

        [HttpGet("tenants")]
        public IActionResult GetActiveTenants()
        {
            var result = _cacheService.GetActiveTenants();
            if (result.Success)
                return Ok(result);
            return BadRequest(result);
        }

        [HttpGet("tenants/sizes")]
        public IActionResult GetAllTenantSizes()
        {
            var result = _cacheService.GetAllTenantSizes();
            if (result.Success)
                return Ok(result);
            return BadRequest(result);
        }

        [HttpGet("tenant/{tenantId}/keys")]
        public IActionResult GetTenantKeys(int tenantId)
        {
            var result = _cacheService.GetTenantKeys(tenantId);
            if (result.Success)
                return Ok(result);
            return BadRequest(result);
        }

        [HttpGet("tenant/{tenantId}/size")]
        public IActionResult GetTenantCacheSize(int tenantId)
        {
            var result = _cacheService.GetTenantCacheSize(tenantId);
            if (result.Success)
                return Ok(result);
            return BadRequest(result);
        }

        [HttpGet("tenant/{tenantId}/entities")]
        public IActionResult GetEntityCounts(int tenantId)
        {
            var result = _cacheService.GetEntityCounts(tenantId);
            if (result.Success)
                return Ok(result);
            return BadRequest(result);
        }

        [HttpGet("tenant/{tenantId}/details")]
        public IActionResult GetCacheDetails(int tenantId)
        {
            var result = _cacheService.GetCacheDetails(tenantId);
            if (result.Success)
                return Ok(result);
            return BadRequest(result);
        }

        [HttpGet("tenant/{tenantId}/entity/{entityName}/keys")]
        public IActionResult GetEntityKeys(int tenantId, string entityName)
        {
            var result = _cacheService.GetEntityKeys(tenantId, entityName);
            if (result.Success)
                return Ok(result);
            return BadRequest(result);
        }

        [HttpGet("item/{key}")]
        public IActionResult GetCacheItemDetail(string key)
        {
            var result = _cacheService.GetCacheItemDetail(key);
            if (result.Success)
                return Ok(result);
            return BadRequest(result);
        }

        [HttpGet("performance")]
        public IActionResult GetPerformanceMetrics()
        {
            var result = _cacheService.GetPerformanceMetrics();
            if (result.Success)
                return Ok(result);
            return BadRequest(result);
        }

        [HttpGet("memory/tenants")]
        public IActionResult GetTenantMemoryInfo()
        {
            var result = _cacheService.GetTenantMemoryInfo();
            if (result.Success)
                return Ok(result);
            return BadRequest(result);
        }

        [HttpGet("memory/tenant/{tenantId}")]
        public IActionResult GetTenantMemoryInfo(int tenantId)
        {
            var result = _cacheService.GetTenantMemoryInfo(tenantId);
            if (result.Success)
                return Ok(result);
            return BadRequest(result);
        }

        [HttpPost("test/memory-quota/{tenantId}")]
        public IActionResult TestMemoryQuota(int tenantId, [FromQuery] int entryCount = 100)
        {
            var result = _cacheService.TestMemoryQuota(tenantId, entryCount);
            if (result.Success)
                return Ok(result);
            return BadRequest(result);
        }
    }
}
